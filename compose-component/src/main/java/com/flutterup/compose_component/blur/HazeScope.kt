package com.flutterup.compose_component.blur

import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.LayoutScopeMarker
import androidx.compose.foundation.layout.internal.JvmDefaultWithCompatibility
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.Layout
import androidx.compose.ui.layout.Measurable
import androidx.compose.ui.layout.MeasurePolicy
import androidx.compose.ui.layout.MeasureResult
import androidx.compose.ui.layout.MeasureScope
import androidx.compose.ui.unit.Constraints
import androidx.compose.ui.zIndex
import dev.chrisbanes.haze.HazeEffectScope
import dev.chrisbanes.haze.HazeState
import dev.chrisbanes.haze.HazeStyle
import dev.chrisbanes.haze.hazeEffect
import dev.chrisbanes.haze.hazeSource
import dev.chrisbanes.haze.rememberHazeState

@Composable
fun HazeContent(
    modifier: Modifier = Modifier,
    state: HazeState = rememberHazeState(),
    content: @Composable HazeScope.(state: HazeState) -> Unit
) {
    Layout(
        content = { HazeScopeInstance(state).content(state) },
        modifier = modifier,
    ) { measurables, constraints ->
        layout(constraints.maxWidth, constraints.maxHeight) {
            measurables.forEach { measurable ->
                val placeable = measurable.measure(constraints)
                placeable.place(0, 0)
            }
        }
    }
}

/** Scope for the children of [HazeContent]. */
@LayoutScopeMarker
@Immutable
interface HazeScope {
    /**
     * @see [dev.chrisbanes.haze.hazeSource]
     */
    @Stable
    fun Modifier.blurSource(
        zIndex: Float = 0f,
        key: Any? = null
    ): Modifier

    /**
     * @see [dev.chrisbanes.haze.hazeEffect]
     */
    @Stable
    fun Modifier.backgroundBlurEffect(
        style: HazeStyle = HazeStyle.Unspecified,
        block: (HazeEffectScope.() -> Unit)? = null,
    ): Modifier

    @Stable
    fun Modifier.contentBlurEffect(
        style: HazeStyle = HazeStyle.Unspecified,
        block: (HazeEffectScope.() -> Unit)? = null,
    ): Modifier
}

internal class HazeScopeInstance(private val hazeState: HazeState) : HazeScope {

    override fun Modifier.blurSource(
        zIndex: Float,
        key: Any?
    ): Modifier = hazeSource(hazeState, zIndex, key)

    override fun Modifier.backgroundBlurEffect(
        style: HazeStyle,
        block: (HazeEffectScope.() -> Unit)?
    ): Modifier = hazeEffect(hazeState, style, block)

    override fun Modifier.contentBlurEffect(
        style: HazeStyle,
        block: (HazeEffectScope.() -> Unit)?
    ): Modifier = hazeEffect(style, block)
}
