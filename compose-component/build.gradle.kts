plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
}

apply(from = "${rootDir}/gradles/buildBase.gradle")
apply(from = "${rootDir}/gradles/buildHilt.gradle")
apply(from = "${rootDir}/gradles/buildCompose.gradle")


android {
    namespace = "com.flutterup.compose_component"
}

dependencies {
    implementation(project(":base"))

    //模糊工具
    api(libs.haze)
    api(libs.haze.materials)
}